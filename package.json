{"name": "the-wild-oasis", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host", "serve": "npm run build && vite preview --host", "serve:prod": "npm run build && serve -s dist -l 3000"}, "dependencies": {"@supabase/supabase-js": "^2.49.1", "@tanstack/react-query": "^4.36.1", "@tanstack/react-query-devtools": "^4.36.1", "date-fns": "^4.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-router-dom": "^6.29.0", "styled-components": "^6.1.15"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "vite": "^4.4.5", "vite-jsconfig-paths": "^2.0.1"}}