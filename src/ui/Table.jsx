import styled from 'styled-components'
import { colors } from '@/styles/constants'
import { createContext, useContext } from 'react'

const StyledTable = styled.div`
  border: 1px solid ${colors['grey-200']};
  font-size: 1.4rem;
  background-color: ${colors['grey-0']};
  border-radius: 7px;
  overflow: hidden;
`

const CommonRow = styled.div`
  display: grid;
  grid-template-columns: ${props => props.columns};
  column-gap: 2.4rem;
  align-items: center;
  transition: none;
`

const StyledHeader = styled(CommonRow)`
  padding: 1.6rem 2.4rem;
  background-color: ${colors['grey-50']};
  border-bottom: 1px solid ${colors['grey-100']};
  text-transform: uppercase;
  letter-spacing: 0.4px;
  font-weight: 600;
  color: ${colors['grey-600']};
`

const StyledRow = styled(CommonRow)`
  padding: 1.2rem 2.4rem;

  &:not(:last-child) {
    border-bottom: 1px solid ${colors['grey-100']};
  }
`

const StyledBody = styled.section`
  margin: 0.25rem 0.25rem;
`

const Footer = styled.footer`
  background-color: ${colors['grey-50']};
  display: flex;
  justify-content: center;
  padding: 1.2rem;

  /* This will hide the footer when it contains no child elements. Possible thanks to the parent selector :has 🎉 */
  &:not(:has(*)) {
    display: none;
  }
`

const Empty = styled.p`
  font-size: 1.6rem;
  font-weight: 500;
  text-align: center;
  margin: 2.4rem;
`

const TableContext = createContext()

function Table({ columns, children }) {
  return (
    <TableContext.Provider value={{ columns }}>
      <StyledTable role="table">{children}</StyledTable>
    </TableContext.Provider>
  )
}

function Header({ children }) {
  const { columns } = useContext(TableContext)
  return (
    <StyledHeader role="row" columns={columns} as="header">
      {children}
    </StyledHeader>
  )
}

function Row({ children }) {
  const { columns } = useContext(TableContext)
  return (
    <StyledRow role="row" columns={columns}>
      {children}
    </StyledRow>
  )
}

function Body({ data, render }) {
  return <StyledBody>{data?.length ? data.map(render) : <Empty>No data to show at the moment</Empty>}</StyledBody>
}

Table.Header = Header
Table.Row = Row
Table.Body = Body
Table.Footer = Footer

export default Table
