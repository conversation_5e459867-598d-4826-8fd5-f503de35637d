import styled from 'styled-components'
import { colors, borderRadius, shadows } from '@/styles/constants'
import { createContext, useContext, useState, useEffect } from 'react'
import { HiEllipsisVertical } from 'react-icons/hi2'
import { createPortal } from 'react-dom'
import { useOutsideClick } from '@/hooks/useOutsideClick'

const Menu = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
`

const StyledToggle = styled.button`
  background: none;
  border: none;
  padding: 0.4rem;
  border-radius: ${borderRadius.sm};
  transform: translateX(0.8rem);
  transition: all 0.2s;

  &:hover {
    background-color: ${colors['grey-100']};
  }

  & svg {
    width: 2.4rem;
    height: 2.4rem;
    color: ${colors['grey-700']};
  }
`

const StyledList = styled.ul`
  position: fixed;

  background-color: ${colors['grey-0']};
  box-shadow: ${shadows.md};
  border-radius: ${borderRadius.md};

  right: ${props => props.position.x}px;
  top: ${props => props.position.y}px;
`

const StyledButton = styled.button`
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  padding: 1.2rem 2.4rem;
  font-size: 1.4rem;
  transition: all 0.2s;

  display: flex;
  align-items: center;
  gap: 1.6rem;

  &:hover {
    background-color: ${colors['grey-50']};
  }

  & svg {
    width: 1.6rem;
    height: 1.6rem;
    color: ${colors['grey-400']};
    transition: all 0.3s;
  }
`

const MenusContext = createContext()

function Menus({ children }) {
  const [openId, setOpenId] = useState('')
  const [position, setPosition] = useState(null)
  const close = () => setOpenId('')
  const open = setOpenId

  useEffect(() => {
    function handleScroll() {
      if (openId) close()
    }

    document.addEventListener('scroll', handleScroll, true)
    return () => document.removeEventListener('scroll', handleScroll, true)
  }, [openId])

  return <MenusContext.Provider value={{ openId, close, open, position, setPosition }}>{children}</MenusContext.Provider>
}

function Toggle({ id }) {
  const { openId, close, open, setPosition } = useContext(MenusContext)

  function handleClick(e) {
    const rect = e.target.closest('button').getBoundingClientRect()
    setPosition({
      x: window.innerWidth - rect.width - rect.x,
      y: rect.y + rect.height + 8
    })
    openId === '' || openId !== id ? open(id) : close()
  }

  return (
    <StyledToggle onClick={handleClick}>
      <HiEllipsisVertical />
    </StyledToggle>
  )
}

function List({ id, children }) {
  const { openId, position, close } = useContext(MenusContext)
  const ref = useOutsideClick({ action: close })

  if (openId !== id) return null

  return createPortal(
    <StyledList ref={ref} position={position}>
      {children}
    </StyledList>,
    document.body
  )
}

function Button({ children, icon, onClick }) {
  const { close } = useContext(MenusContext)

  function handleClick() {
    onClick?.()
    close()
  }

  return (
    <li>
      <StyledButton onClick={handleClick}>
        {icon}
        <span>{children}</span>
      </StyledButton>
    </li>
  )
}

Menus.Menu = Menu
Menus.Toggle = Toggle
Menus.List = List
Menus.Button = Button

export default Menus
