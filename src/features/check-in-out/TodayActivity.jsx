import styled from 'styled-components'
import { colors, borderRadius } from '@/styles/constants'

import Heading from '../../ui/Heading'
import Row from '../../ui/Row'

const StyledToday = styled.div`
  background-color: ${colors['grey-0']};
  border: 1px solid ${colors['grey-100']};
  border-radius: ${borderRadius.md};

  padding: 3.2rem;
  display: flex;
  flex-direction: column;
  gap: 2.4rem;
  grid-column: 1 / span 2;
  padding-top: 2.4rem;
`

const TodayList = styled.ul`
  overflow: scroll;
  overflow-x: hidden;

  /* Removing scrollbars for webkit, firefox, and ms, respectively */
  &::-webkit-scrollbar {
    width: 0 !important;
  }
  scrollbar-width: none;
  -ms-overflow-style: none;
`

const NoActivity = styled.p`
  text-align: center;
  font-size: 1.8rem;
  font-weight: 500;
  margin-top: 0.8rem;
`

function Today() {
  return (
    <StyledToday>
      <Row type="horizontal">
        <Heading as="h2">Today</Heading>
      </Row>
    </StyledToday>
  )
}

export default Today
